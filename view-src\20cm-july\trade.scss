.v20cm-july {

	box-sizing: border-box;
	height: 100%;
	background-color: #0C1016;
	display: flex;
	flex-direction: row;

	.part-left-view {

		width: 886px;
		height: 100%;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;

		&.has-order-queue {
		
			.view-strategy {

				width: 100%;
				flex: 0 0 255px;
				overflow-y: hidden;
			}
		}
	
		&.no-order-queue {
		
			.view-strategy {
	
				width: 100%;
				flex: 0 0 290px;
				overflow-y: hidden;
			}
		}
	}

	.part-right-view {

		flex-grow: 1;
		flex-shrink: 1;
		width: 50%;
		height: 100%;
		display: flex;
		flex-direction: column;
		box-sizing: border-box;
		overflow: hidden;
	}

	.view-task {

		width: 100%;
		flex: 1 1 300px;
		overflow-y: hidden;
	}
	
	.view-queue {

		width: 100%;
		flex: 0 0 290px;
		padding-top: -30px;
		overflow-y: auto;
	}

	.view-main {

		width: 100%;
		flex: 0 0 545px;
	}

	.trade-view-block {

		box-sizing: border-box;
		height: 100%;
		padding-top: 28px;
		background-color: #1A212B;

    	.batchs {

			float: right;
			margin-right: 20px;

			.item {

				cursor: pointer;
				margin-left: 10px;
				padding: 4px 10px;
				border-radius: 2px;

				&.start {
					background-color: #1BBE48;
				}

				&.stop {
					background-color: #ef3939;
				}
			}
		}
	}

	.task-ordered {
		.smart-table {
			height: 100%;
		}
	}

	.regular-view {

		box-sizing: border-box;
		padding: 2px;
	}

	.view-toolbar {

		height: 28px;
		margin-top: -28px;
		line-height: 28px;
		overflow: hidden;
		background-color: #253650;

		&.strategy-toolbar {
			padding-right: 290px;
		}

		.el-tabs__header {

			margin: 0;
			border-bottom: none;
		}

		.el-tabs--card>.el-tabs__header .el-tabs__nav {
			border: none;
		}

		.el-tabs__item {

			margin-top: 2px;
			height: 28px;
			line-height: 26px;
			padding: 0 10px !important;
			border-top-left-radius: 6px;
    		border-top-right-radius: 6px;
			font-size: 12px;
			color: #fff;
			border: none;
		}

		.el-tabs__item.is-active {

			background-color: #1A212B;
			color: #fff;
		}

		.el-tabs__nav-next,
		.el-tabs__nav-prev {
			line-height: 30px;
		}

		.strategy-tools {

			display: block;
			float: right;
			margin-right: -280px;
    		margin-top: -29px;
		}

		.el-input,
		.el-select,
		.el-autocomplete,
		.el-input__inner {
			height: 24px;
		}

		.el-input__icon {
			line-height: 24px;
		}
	}

	.view-title {
		font-weight: bold;
	}

	.view-task {

    .cache-btn {
      padding: 2px 10px;
      margin-left: 10px;
    }

		.tables2 {
			box-sizing: border-box;
			overflow: hidden;
		}

		.xsplitter .splitter-bar {
			background-color: #2C79F2;
		}

		.table-task-2-ext {

			box-sizing: border-box;

			.smart-table {
				padding-top: 0 !important;
			}

			.smart-table-header {
				display: none;
			}

			.smart-table-body table tr:nth-child(even) {
				background-color: #1B3C78;
			}
			
			.smart-table-body table tr:nth-child(odd) {
				background-color: #1B3C78;
			}
		}
		
		.row-oper-icon {

			display: inline-block;
			padding-left: 5px;
			padding-top: 2px;
			width: 18px;
			height: 16px;
			text-align: center;
			background-color: #345992;

			&:hover {
				background-color: #1667e3;
			}

			.iconfont {
				font-size: 14px;
			}
		}
	}

	&.convinient-reading {

		.smart-table {

			padding-top: 30px !important;
			font-size: 14px;

			&::-webkit-scrollbar {
				width: 8px !important;
				height: 8px !important;
			}

			thead tr,
			tbody tr {
				height: 30px !important;
			}
		}

		.row-oper-icon {

			width: 30px;
			height: 20px;

			.iconfont {
				position: relative;
				top: -2px;
				font-size: 20px;
			}
		}

		.view-toolbar {
			.el-tabs__item.is-active {
				background-color: #8d2525;
				font-weight: bold;
			}
		}
	}

	.view-entrust {

		.smart-table {
			height: 100%;
		}
	}

	.entrust-options {

		position: absolute;
		z-index: 1;
		left: 175px;
		top: -28px;
	}

	.view-strategy {

		.block {

			display: block;
			float: left;
			height: 100%;
			box-sizing: border-box;
			overflow-x: hidden;
			overflow-y: auto;
		}

		.block1 {

			width: 7%;
			text-align: center;

			button {

				margin-top: 7px;
				width: 28px;
				padding: 5px 0 0 0;
				overflow: hidden;

				&.btn01 {
					height: 80px;
				}

				&.btn02 {
					height: 46px;
				}

				span {

					display: inline-block;
					box-sizing: border-box;
					padding: 0 5px;
					width: 100%;
					height: 100%;
					line-height: 18px;
					word-break: break-all;
					word-wrap: break-word;
					white-space: pre-wrap;
					font-weight: bold;
				}
			}
		}

		.block2 {
			padding-bottom: 5px;
      position: relative;
		}
		
		.block2,
		.block3,
		.block4 {

			width: 31%;
			padding-left: 10px;
			border-left: 1px solid #345992;
		}

		.block-title {

			position: sticky;
			top: 0;
			z-index: 2;
			line-height: 24px;
			font-weight: 700;
			background-color: #1A212B;
			color: #8CB5ED;
		}

		.figuring {

			margin-top: 8px;
			margin-bottom: 12px;

			.hands {

				padding-left: 10px;
				cursor: default;
			}
		}

		.block-row {

			margin-top: 6px;

      &.inline {
        // display: inline-block;
        // margin-right: 10px;
        position: absolute;
        top: 0px;
        right: 90px;
        z-index: 2;
        & + .inline {
          right: 10px;
        }
      }

			.conditions-box + .conditions-box {
				margin-left: 5px;
			}

			.el-input,
			.el-input-number {
				width: 60px;
			}

			.el-input__inner {

				height: 24px;
				line-height: 24px;
				padding: 0 3px;
			}

			.el-select {
				width: 100px;
				margin-left: 5px;
				.el-input {
					width: 100px;
				}
			}

			.compare {
				padding: 0 5px;
			}

			.unit {

				display: inline-block;
				margin-bottom: -8px;
				height: 24px;
				line-height: 24px;
				width: 32px;
				text-align: center;
				overflow: hidden;
				background-color: #2A3B53;
			}

			.el-date-editor {

				width: 180px;
				.el-range__icon {

					top: -4px;
					left: 2px;
				}
			}

			.pct-shortcut-panel {

				margin-top: 5px;
				display: flex;
				gap: 5px;

				.pct-shortcut {

					background-color: #555;
					padding: 2px 3px;
					border-radius: 3px;
					cursor: default;
				}
			}
		}
	}

	.view-queue {

		.title {
			font-weight: bold;
		}

		.infinite-list {

			margin: 0;
			padding: 0;
			height: 100%;
			overflow-y: scroll;
			overflow-x: hidden;
		}
	
		.remote {
	
			display: inline-block;
			box-sizing: border-box;
			width: 51.4px;
			height: 28px;
			line-height: 28px;
			padding-left: 3px;
			text-align: center;
			font-weight: bold;
			cursor: default;
	
			&.highlighted {
				background-color: #942C2C;
			}
	
			&.large-scale {
				background-color: #D28181;
			}
		}
	}

	.tabbed-radios-external {

		.el-radio-group {
			margin-bottom: -1px;
		}

		.el-radio-button__inner {

			border-top-left-radius: 6px;
			border-top-right-radius: 6px;
			border: none;
			background-color: transparent;
		}

		.el-radio-button__orig-radio:checked+.el-radio-button__inner {
			background-color: #1A212B;
		}

		.el-radio-button--mini .el-radio-button__inner {
			padding: 5px 15px;
		}
	}

	.footer-row {

		position: absolute;
		z-index: 999;
		bottom: 0;
		right: 240px;
		width: 500px;
		height: 32px;
		box-sizing: border-box;

		.footer-row-inner {

			height: 100%;
			line-height: 32px;
			padding-left: 20px;
			// background-color: #283A56;
			overflow: hidden;
			text-align: right;

			.summary-item {
				padding-right: 20px;
			}
		}
	}

	.setting-btn {

		position: absolute;
		right: 20px;
    	top: 40px;

		.el-button {
			height: 18px;
		}
	}

	.setting-form {
		
		.rington-item {

			margin-top: 10px;
			padding-left: 70px;
			padding-right: 35px;
			box-sizing: border-box;
		}

		.ring-name {

			display: inline-block;
			margin-left: -60px;
			width: 60px;
		}

		.setting-item {
			height: 32px;
    		display: flex;
    		align-items: center;
		}

		.setting-name {

			width: 90px;
			display: inline-block;
			overflow: hidden;
		}

		.setting-input-desc {
			display: inline-block;
		}

		.el-select {
			width: 100%;
		}

		.play-btn {

			display: block;
			float: right;
			margin-right: -20px;
			margin-top: 3px;
			font-size: 16px;
		}

		.custom-media {

			display: block;
			line-height: 24px;
			color: red;

			&:hover {
				text-decoration: underline;
			}
		}
	}
}

.win-top-tabs {

	.tab-operation {
		display: none !important;
	}
}