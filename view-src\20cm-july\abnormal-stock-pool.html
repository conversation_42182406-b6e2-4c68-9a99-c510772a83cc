<div class="abnormal-stock-pool-view v20cm-july" v-cloak>
  <div class="regular-view">
    <div class="action-row">
      <div class="filter-controls">
        <el-input v-model="state.searchText" placeholder="搜索股票代码或名称" size="small" style="width: 200px; margin-left: 10px" clearable>
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
      <el-button icon="el-icon-refresh" type="primary" @click="refresh">刷新</el-button>
    </div>
    <div class="risk-table">
      <el-table row-key="id" :data="filteredData">
        <el-table-column show-overflow-tooltip v-for="col in columns" :key="col.prop" :prop="col.prop" :label="col.label" :width="col.width">
          <template slot-scope="scope"> {{ col.render ? col.render(scope.row) : scope.row[col.prop] }} </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</div>
