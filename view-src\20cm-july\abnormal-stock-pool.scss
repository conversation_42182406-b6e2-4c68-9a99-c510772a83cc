@mixin el-table-styles {
  &::before {
    background-color: transparent;
  }
  th,
  tr,
  td {
    background-color: #1e2836;
    transition: none;
  }
  .el-table__body {
    tr {
      &.current-row {
        & > td {
          background-color: #2c79f2;
        }
      }
      &.hover-row {
        & > td {
          background-color: #324f80;
        }
      }
    }
  }
  .el-table__header-wrapper,
  .el-table__fixed-header-wrapper {
    th {
      padding: 0 10px;
      border-right: 1px solid #0c1016;
      background-color: #1e2836;
      line-height: 25px;
      border-bottom-color: #0c1016;
      .cell {
        font-size: 14px;
        color: #8cb5ed;
        padding-left: 0 !important;
      }
    }
  }
  .el-table__fixed-right::before,
  .el-table__fixed::before {
    background-color: transparent;
  }
  .el-table__row {
    td {
      border-bottom-color: #0c1016;
      border-right: 1px solid #0c1016;
      font-size: 14px;
      padding: 0;
      .cell {
        color: #fff;
        line-height: 32px;
        .flag {
          height: unset;
          padding: 3px 6px;
          * {
            font-size: 14px;
          }
        }
        .status {
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &.pause {
            i {
              color: #8bc34a;
            }
          }
          &.play {
            i {
              color: #ff9800;
            }
          }
          &.check {
            i {
              color: #4caf50;
            }
          }
          &.create {
            i {
              color: #9e9e9e;
            }
          }
          .status-icon {
            font-size: 14px;
          }
        }
        .el-progress {
          &-bar {
            padding-right: 30px;
            margin-right: -35px;
          }
          &__text {
            color: #fff;
          }
        }
      }
    }
  }
  .el-table__empty-block {
    min-height: 10px;
    .el-table__empty-text {
      line-height: 25px;
      font-size: 14px;
      color: #fff;
    }
  }
}
.abnormal-stock-pool-view {
  height: 100%;
  .regular-view {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    .action-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 40px;
      padding: 0 10px;

      .el-button * {
        font-size: 14px;
      }
    }
    .risk-table {
      .el-table {
        @include el-table-styles;
        background-color: transparent;
      }
      .action-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        padding: 0 10px;

        .el-button * {
          font-size: 14px;
        }
      }
    }
  }
}

.el-select-dropdown__item {
  font-size: 14px;
}
