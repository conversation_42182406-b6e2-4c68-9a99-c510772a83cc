const { <PERSON><PERSON>erWindow } = require('@electron/remote');
const { IView } = require('../../component/iview');
const { NumberMixin } = require('../../mixin/number');
const { AccountSimple } = require('../20cm/components/objects');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { ZtStandardStrategy, ZtSetting, TaskObject, BuyTask, Entrance, FromTable, RowBehaviors, mergeLocalAndRemoteStrategies } = require('./objects');
const { SecAccount } = require('../../model/sec-account');
const { repo20Cm } = require('../../repository/20cm');
const { repoAccount } = require('../../repository/account');
const { repoAccountAround } = require('../../repository/account-around');
const { repoSecAccount } = require('../../repository/sec-account');
const MouseTrap = require('mousetrap');
const { captureStockCode } = require('../../libs/plugin/ths');
const { BizHelper } = require('../../libs/helper-biz');
const { WinPosSizeMgr } = require('../../toolset/win-pos-size-mgr');

module.exports = class TradeView extends IView {

    constructor(view_name, is_standalone_window) {

        super(view_name, is_standalone_window, '涨停板策略交易');
        this.strategies = [new ZtStandardStrategy()].splice(1);
        this.accounts = [new AccountSimple({})].splice(1);

        /**
         * 账号资产总览
         */
        this.summary = {

            asset: null,
            available: null,
            equity: null,
            credit: null,
        };

        /**
         * 当前，源自监控中的任务、已下单任务、已撤单任务，选中的任务状况
         */
        this.selected = {

            /** 来自于哪个表格的选中 */
            from: null,
            /** 选中的任务 */
            task: this.typedsTask(null),
        };

        var ringtons = Entrance.makeRings();
        this.ztsetting = ZtSetting.makeDefault();
        this.ztrington = {

            entrusted: ringtons[0].code,
            bought: ringtons[1].code,
            canceled: ringtons[2].code,
            sold: ringtons[3].code,

            customized: {

                entrusted: null,
                bought: null,
                canceled: null,
                sold: null,
            },
        };

        /**
         * 最近一次收到服务器端的回复推送
         */
        this.lastReply = { code: 0, time: 0 };
        this.states = {
            /** 最新添加的监控使用的策略名称 */
            latestAddedStrategyName: null,
        };

        /** 是否柜台离线时登录 */
        this.offlineCounter = false;
        this.offlineCounterFlag = false;

        /**
         * 是否支持涨停对列
         */
        this.isOrderQueueSupported = true;
    }

    get sortedTables() {

        return [

            { from: FromTable.added, table: this.vtask.tableObj },
            { from: FromTable.canceled, table: this.vtask.tableObj2 },
            { from: FromTable.ordered, table: this.vordered.tableObj },
        ];
    }

    /**
     * @param {BuyTask} task
     */
    typedsTask(task) {
        return task;
    }

    buildLayout() {

        var $root = this.$root = this.$container.querySelector('.v20cm-july');
        var $left = $root.querySelector('.part-left-view');
        var $taskRoot = $root.querySelector('.view-task.left-part');
        var $taskOrderedRoot = $root.querySelector('.view-task.right-part');
        var $strategyRoot = $root.querySelector('.view-strategy');
        var $queueRoot = $root.querySelector('.view-queue');
        var $mainRoot = $root.querySelector('.view-main');

        if (this.isOrderQueueSupported) {
            $left.classList.add('has-order-queue');
        }
        else {
            $left.classList.add('no-order-queue');
        }

        /**
         * 上方，左侧，任务列表
         */
        const TaskView = require('./task');
        var vtask = new TaskView();
        vtask.loadBuild($taskRoot);
        vtask.attachRootObj(this);
        vtask.registerEvent('tolerate', this.handleToleration.bind(this));
        vtask.registerEvent('task-added', this.handleTaskAdded.bind(this));
        vtask.registerEvent('refresh-task', this.requestTasks.bind(this));
        this.vtask = vtask;

        /**
         * 上方，右侧，已买监控
         */
        const OrderedTaskView = require('./task-ordered');
        var vordered = new OrderedTaskView();
        vordered.loadBuild($taskOrderedRoot);
        vordered.registerEvent('tolerate', this.handleToleration.bind(this));
        vordered.registerEvent('refresh-task', this.requestTasks.bind(this));
        this.vordered = vordered;

        /**
         * 中间，左侧，策略参数表单
         */
        const StrategyView = require('./strategy');
        var vstrag = new StrategyView();
        vstrag.loadBuild($strategyRoot);
        vstrag.registerEvent('switched', this.handleStrategyChange.bind(this));
        vstrag.registerEvent('param-updated', this.handleStrategyChange.bind(this));
        vstrag.registerEvent('start-task', this.handleStart.bind(this));
        vstrag.registerEvent('stop-task', this.handleStop.bind(this));
        vstrag.registerEvent('cancel-task', this.handleCancel.bind(this));
        vstrag.registerEvent('to-save', this.saveStrategy.bind(this));
        this.vstrag = vstrag;

        if (this.isOrderQueueSupported) {

            /**
             * 左下角，涨停排单
             */
            const QueueView = require('./queue');
            var vqueue = new QueueView();
            vqueue.loadBuild($queueRoot);
            this.vqueue = vqueue;
        }
        else {
            $queueRoot.remove();
        }

        /**
         * 右侧，中下方工作区
         */
        const MainView = require('./main');
        var vmain = new MainView(true);
        vmain.registerEvent('popout', this.handlePopoutRequest.bind(this));
        vmain.registerEvent('inst-change', this.handleStockChange.bind(this));
        vmain.loadBuild($mainRoot);
        this.vmain = vmain;
    }

    ask4Strategy(strategy_name) {
        return this.vstrag.getDefaultSetting(strategy_name);
    }

    handlePopoutRequest() {

        if (this.popid) {

            let winObj = BrowserWindow.fromId(this.popid);
            if (winObj && !winObj.isDestroyed()) {

                winObj.focus();
                return;
            }
        }

        this.renderProcess.once(this.systemEvent.huntWinTabViewFromRender, (event, windowId) => {

            this.popid = windowId;
            var winRef = BrowserWindow.fromId(windowId);

            winRef.on('closed', () => {

                winRef = null;
                this.popid = undefined;
            });

            winRef.webContents.on('is-ready', () => {
                //
            });
        });

        var woptions = {

            minWidth: 500,
            minHeight: 400,
            width: 552,
            height: 432,
            minimizable: true,
            maximizable: false,
            highlight: true,
        };

        this.log(`to popout a sperated trading window`);
        this.renderProcess.send(this.systemEvent.huntWinTabViewFromRender, '@20cm-july/main', `全息交易窗口`, woptions);
    }

    push2Win(sells) {

        if (!this.popid) {
            return;
        }

        var winRef = BrowserWindow.fromId(this.popid);
        if (winRef && !winRef.isDestroyed()) {
            winRef.webContents.send('push-sells', sells);
        }
    }

    openSetting() {

        if (this.vsetting == undefined) {

            const { enable_ths_shortcut, ths_capture, ths_capture_2 } = this.app.shortcutOptions; 
            var SettingView = require('./setting');
            var vsetting = new SettingView();
            vsetting.loadBuild(this.$root.querySelector('.setting-view-box'));
            vsetting.registerEvent('ztsetting-change', this.handleSettingChange.bind(this));
            vsetting.affectShortcuts(!!enable_ths_shortcut, ths_capture, ths_capture_2);
            this.vsetting = vsetting;
        }

        if (this.isThsCaptureSupported()) {

            let strategies = this.strategies.map(x => x.name);
            this.vsetting.exposeThsChoice();
            this.vsetting.setStrategies(strategies);
        }

        this.vsetting.update2Latest(this.helper.deepClone(this.ztsetting));
        this.vsetting.showup();
        this.log(`opened the setting dialog`);
    }

    openRingtonSetting() {

        if (this.vrington == undefined) {

            var SettingRingtonView = require('./setting-rington');
            var vrington = new SettingRingtonView();
            vrington.loadBuild(this.$root.querySelector('.rington-view-box'));
            vrington.registerEvent('rington-change', this.handleRingtonChange.bind(this));
            this.vrington = vrington;
        }

        this.vrington.update2Latest(this.helper.deepClone(this.ztrington));
        this.vrington.showup();
        this.log(`opened the rington setting dialog`);
    }

    syncAccountData() {

        if (!(this.accounts instanceof Array) || this.accounts.length == 0) {
            return this.interaction.showError('账号数据暂未获取到，请稍后重试。');
        }

        let first = this.accounts[0];
        this.interaction.showConfirm({

            title: '操作确认',
            message: `是否确认，对账号 [${first.accountName}] 进行数据同步？`,
            confirmed: () => { this.overlapAll(); },
        });
    }

    async overlapAll(silence = false) {

        if (this.isOverlaping) {
            return;
        }

        this.isOverlaping = true;

        try {

            for (let i = 0; i < this.accounts.length; i++) {

                let each = this.accounts[i];
                let resp = await repoAccountAround.overlap(each.accountId, true);
            }

            if (!silence) {
                this.interaction.showSuccess(`账号同步请求已发送`);
            }

            this.requestAccounts();
        }
        catch(ex) {
            console.error(ex);
        }
        finally {
            this.isOverlaping = false;
        }
    }

    handleTaskAdded(strategy_name) {

        this.states.latestAddedStrategyName = strategy_name;
        this.overlapAll(true);
    }

    createHeaderApp() {

        new Vue({

            el: this.$root.querySelector('.setting-btn'),
            methods: this.helper.fakeVueInsMethod(this, [
                this.openSetting,
                this.openRingtonSetting,
                this.syncAccountData,
            ]),
        });
    }

    /**
     * @param {*} from 来自哪个表格的触发
     * @param {*} behavior 触发行为 （select | delete）
     * @param {BuyTask} task 事件触发的来源监控任务
     */
    handleToleration(from, behavior, task) {

        var sorted = this.sortedTables;
        var current = sorted.find(x => x.from == from).table;
        var others = sorted.filter(x => x.from != from).map(x => x.table);

        if (behavior == RowBehaviors.select) {

            this.setAsSelected(from, task);
            others.forEach(t => { t.unselectRow(); });
        }
        else if (behavior == RowBehaviors.delete) {

            /**
             * 当所有表格，都没有数据时，作状态协同和清理
             */

            let least = sorted.find(x => x.table.rowCount > 0);
            if (least == undefined) {
                this.handleNoneSelected();
            }
        }
    }

    /**
     * 设置为当前，被选中的 | 被删除了的，监控任务
     * @param {*} from 表格来源
     * @param {BuyTask} task 任务数据行 （可能为选中的，也可能为已被删除的，也可能为空）
     */
    async setAsSelected(from, task) {

        var { instrument, instrumentName } = task || {};

        /**
         * 当前上下文，处于选中状态的监控
         */
        Object.assign(this.selected, { from, task });
        await this.vstrag.handlePassiveSwitch(task);
        this.isOrderQueueSupported && this.vqueue.setAsInstrument(instrument, instrumentName, task ? task.id : null);
        this.vmain.keepAligned(instrument, instrumentName);
    }

    handleNoneSelected() {

        this.log(`stock info is cleared for views`);
        this.vstrag.clearTask();
        this.isOrderQueueSupported && this.vqueue.setAsInstrument(null, null, null);
        this.vmain.keepAligned(null, null);
    }

    handleStockChange(instrument, instrumentName) {
        this.isOrderQueueSupported && this.vqueue.setAsInstrument(instrument, instrumentName, null);
    }

    /**
     * @param {ZtStandardStrategy} strategy
     */
    handleStrategyChange(strategy) {

        const sltd = this.selected;
        if (!sltd.task) {
            return;
        }

        if (sltd.from == FromTable.added || sltd.from == FromTable.canceled) {
            this.vtask.outupdate(sltd.task, strategy);
        }
        else if (sltd.from == FromTable.ordered) {
            this.vordered.outupdate(sltd.task, strategy);
        }
    }

    handleStart() {
        this.vtask.outstart(this.selected.task);
    }

    handleStop() {
        this.vtask.outstop(this.selected.task);
    }

    handleCancel() {
        this.vordered.outcancel(this.selected.task);
    }

    /**
     * @param {TaskObject} data
     * @param {*} reqId
     */
    handleTaskStartedReply(data, reqId) {

        this.log(`a new task is created, and the reply is: ${JSON.stringify(data)}`);
        this.vtask.attachTaskId(data, reqId);
    }

    /**
     * @param {Array<ZtStandardStrategy>} strategies
     */
    saveStrategy(strategies) {

        this.strategies = strategies;
        this.mergeSettings();
    }

    /**
     * @param {ZtSetting} setting
     */
    handleSettingChange(setting) {

        Object.assign(this.ztsetting, this.helper.deepClone(setting));
        this.setReadingMode(setting.biggerFont);
        this.brocastSettingChange();
        this.mergeSettings();
    }

    handleRingtonChange(rington) {

        this.ztrington = this.helper.deepClone(rington);
        this.brocastRingtonChange();
        this.mergeSettings();
    }

    async mergeSettings() {

        var settings = {

            strategies: this.strategies,
            setting: this.ztsetting,
            rington: this.ztrington,
            defaultParam: this.defaultParam,
        };

        var resp = await repo20Cm.setting(settings);
        if (resp.errorCode != 0) {

            this.interaction.showError(`设置保存失败：${resp.errorCode}/${resp.errorMsg}`);
            return;
        }

        this.interaction.showSuccess('设置已保存');
    }

    /**
     * @param {Array<ZtStandardStrategy>} strategies
     */
    mergeLocalSetting(strategies) {

        let settings = TaskObject.transerTriggers(this.getContextDataItem(this.dataKey.logInInput));
        let { buys, cancels } = settings;
        if (Array.isArray(strategies)) {
            strategies.forEach(strag => {
                mergeLocalAndRemoteStrategies(strag, this.helper.deepClone(buys), this.helper.deepClone(cancels));
            });
        }
    }

    async requestOrderQueueCapab() {

        try {

            let resp = await repoSecAccount.query();
            let { data, errorCode, errorMsg } = resp;
            let secAccounts = Array.isArray(data) ? data.map(x => new SecAccount(x)) : [];
            let accounts = Array.isArray(this.accounts) ? this.accounts : [];
            let supporteds = accounts.filter(x => secAccounts.some(y => x.financeAccount == y.financeAccount && y.needOrder === true));

            /**
             * 仅当全部的账号，都开启了涨停队列时，才进行开启
             */
            this.isOrderQueueSupported = accounts.length > 0 && supporteds.length == accounts.length;
        }
        catch (ex) {
            console.error(ex);
        }
    }

    async requestSettings() {

        var resp = await repo20Cm.querySetting();

        try {

            var user_settings = JSON.parse((resp.data || {}).settings || '{}');
            var { strategies, setting, rington, defaultParam } = user_settings;
            this.defaultParam = defaultParam || {};
            this.strategies = strategies instanceof Array ? strategies : [];
            this.mergeLocalSetting(strategies);

            if (this.helper.isJson(setting)) {

                Object.assign(this.ztsetting, this.helper.deepClone(setting));
                this.setReadingMode(this.ztsetting.biggerFont);
                this.brocastSettingChange();
                setTimeout(() => { this.brocastSettingChange(); }, 1000 * 5);
            }

            if (this.helper.isJson(rington)) {

                this.ztrington = rington;
                this.brocastRingtonChange();
                setTimeout(() => { this.brocastRingtonChange(); }, 1000 * 5);
            }
        }
        catch(ex) {
            console.error(ex);
        }
    }

    /**
     * @param {Boolean} is_bigger_font
     */
    setReadingMode(is_bigger_font) {

        let class_name = 'convinient-reading';
        if (is_bigger_font) {
            this.$root.classList.add(class_name);
        }
        else {
            this.$root.classList.remove(class_name);
        }
    }

    brocastSettingChange() {

        var ref = this.ztsetting;
        this.vordered.setAsSetting(ref);
        this.vmain.setAsSetting(ref);
    }

    brocastRingtonChange() {

        var ref = this.ztrington;
        this.vtask.setAsRington(ref);
        this.vordered.setAsRington(ref);
        this.vmain.setAsRington(ref);
    }

    createFooterApp() {

        new Vue({

            el: this.$root.querySelector('.footer-row'),
            data: {
                summary: this.summary,
            },
            mixins: [NumberMixin],
        });
    }

    /**
     * @param {Array<TaskObject>} records
     */
    handleNotify(records) {
        console.log('trade notify',records)
        this.log(`task change notifications: ${JSON.stringify(records)}`);

        if (records.length == 0) {
            return;
        }

        /**
         * 对推送的数据，按照ID（即为创建时间）进行倒序排列
         */

        records.sort((x, y) => { return x.id > y.id ? -1 : x.id < y.id ? 1 : 0; });

        var ref = this.systemTrdEnum.tradingDirection;

        // const autoTypes = [98, 101, 103, 109];
        const autoBuyTypes = [98, 109];
        var autos = records.filter(task => autoBuyTypes.some(type => type == task.boardStrategy.strategyType));
        var buys = records.filter(task => !autoBuyTypes.some(type => type == task.boardStrategy.strategyType) && task.direction == ref.buy.code);

        if (buys.length > 0) {

            const by = { money: 7, ratio: 9 };
            const byCredit = { money: 5, ratio: 6 };

            buys.forEach(task => {
                
                if (task.limitPositionType == byCredit.money) {
                    task.limitPositionType = by.money;
                }
                else if (task.limitPositionType == byCredit.ratio) {
                    task.limitPositionType = by.ratio;
                }

                if (this.helper.isNone(task.customStrategy) && this.states.latestAddedStrategyName) {
                    task.customStrategy = this.states.latestAddedStrategyName;
                }
                
                let matched = this.strategies.find(stra => stra.name == task.customStrategy);
                if (matched) {
                    task.customStrategy = matched.name;
                }
                else {
                    task.customStrategy = '临时策略';
                }
            });

            let login_input = this.getContextDataItem(this.dataKey.logInInput);
            let buyTasks = buys.map(task => {

                let settings = TaskObject.transerTriggers(login_input);
                return TaskObject.Convert2BuyTask(task, settings.buys, settings.cancels);
            });

            let buyTasks2 = buys.map(task => {

                let settings = TaskObject.transerTriggers(login_input);
                return TaskObject.Convert2BuyTask(task, settings.buys, settings.cancels);
            });
            this.vtask.push(buyTasks);
            this.vordered.push(buyTasks2);
            this.vstrag.tryUpdateTaskStatus(buyTasks);
            this.isOrderQueueSupported && this.vqueue.mapOrders(records);
        }

        if (autos.length > 0) {

            this.vmain.push(autos);
            this.push2Win(autos);
        }

        this.tryStickySelect();
    }

    autoSelectFirst() {

        let tables = [

            this.vtask.tableObj,
            this.vtask.tableObj2,
            this.vordered.tableObj,
        ];

        let first = tables.find(x => x.rowCount > 0);
        if (first) {
            first.selectNextRow();
        }
    }

    /**
     * @param {Uint8Array} quote_byte_data
     */
    handlePipeMsg(quote_byte_data) {

        try {

            let quote = null;
            if (typeof quote_byte_data == 'string') {
                quote = JSON.parse(quote_byte_data);
            }
            else {
                let str_content = Buffer.from(quote_byte_data).toString('utf-8');
                quote = JSON.parse(str_content);
            }

            this.vtask.handlePipeMsg(quote);
        }
        catch(ex) {
            this.interaction.showError(`接收到行情信号，解析发生错误[type=${typeof quote_byte_data}]`);
        }
    }

    /**
     * @param {BuyTask} record
     */
    identifyTask(record) {
        return record.id || record.localId;
    }

    async tryStickySelect() {

        const { task, from } = this.selected;
        if (!task) {
            return;
        }

        let which = this.sortedTables.find(x => x.table.hasRow(this.identifyTask(task)));
        if (which == undefined) {
            console.log('sticky select: reset');
            await this.setAsSelected(null, null);
            return;
        }

        let is_still_there = which.from == from;
        if (is_still_there) {

            /**
             * 选中的监控，还在原表
             */
        }
        else {
            which.table.selectRow(this.identifyTask(task));
        }
    }

    isTooFast(code) {

        let now = new Date().getTime();
        let last = this.lastReply;
        let shouldShow = code != last.code || (now - last.time) > 1599;

        if (shouldShow) {

            last.code = code;
            last.time = now;
            return false;
        }
        else {
            return true;
        }
    }

    /**
     * @param {Number} code
     * @param {String} mean
     */
    listen2Reply(code, mean) {

        const CReply = Cm20FunctionCodes.reply;
        this.renderProcess.on(code.toString(), (event, { data, errorCode, errorMsg }, reqId) => {

            if (code == CReply.started && errorCode == 0) {
                this.handleTaskStartedReply(data, reqId);
            }

            if (errorCode == 0 && this.isTooFast(code)) {
                return;
            }

            // 删除task,历史原因删除缓存task可能报错
            if (errorCode == 5503) {
              return;
            }

            errorCode == 0 && this.interaction.showSuccess(`${mean}，已处理`);
            errorCode != 0 && console.error({ errorCode, errorMsg, data });
            errorCode != 0 && this.interaction.showError(`处理发生错误：${errorMsg}`);
        });
    }

    listen2Replies() {

        const CReply = Cm20FunctionCodes.reply;
        var creplies = [

            { code: CReply.started, mean: '启动' },
            { code: CReply.stopped, mean: '停止' },
            { code: CReply.deleted, mean: '删除' },
            { code: CReply.canceled, mean: '撤单' },
            { code: CReply.mannualBuy, mean: '手动买入' },
        ];

        creplies.forEach(reply_item => {
            this.listen2Reply(reply_item.code, reply_item.mean);
        });
    }

    listen2Notifies() {

        this.renderProcess.on(Cm20FunctionCodes.reply.queried.toString(), (event, { data, errorCode, errorMsg }, reqId) => {
            errorCode == 0 && this.handleNotify(data, reqId);
            this.autoSelectFirst();
        });
        this.renderProcess.on(Cm20FunctionCodes.reply.started.toString(), (event, res, reqId) => { 
            
            if (res.errorCode !== 0) {
                this.interaction.showError(`启动失败：${res.errorCode}/${res.errorMsg}`);
            } else {
                this.handleNotify([res.data], reqId)
            }
        });
        this.renderProcess.on(Cm20FunctionCodes.reply.modified.toString(), (event, resp, reqId) => {
            if (resp.errorCode != 0) {
                    
            this.interaction.showError(`保存失败：${resp.errorCode}/${resp.errorMsg}`);
            }
        });

        this.renderProcess.on(Cm20FunctionCodes.reply.created.toString(), (event, resp, reqId) => {
            if (resp.errorCode === 0) {
                this.handleNotify([resp.data], reqId);
            }
        });

        const cnotify = Cm20FunctionCodes.notify;
        this.renderProcess.on(cnotify.created.toString(), (event, resp, reqId) => { 
            if (resp.errorCode != 0) {
                this.interaction.showError(`${resp.errorMsg}`);
            }
            this.handleNotify([resp], reqId);
        });
        this.renderProcess.on(cnotify.changed.toString(), (event, resp, reqId) => { 

            if (resp.errorCode != 0) {
                this.interaction.showError(`${resp.errorMsg}`, {
                    duration: 0,
                    showClose: true,
                });
            }
            this.handleNotify([resp], reqId);
         });
        this.renderProcess.on(cnotify.deleted.toString(), (event, resp, reqId) => { 
            if (resp.errorCode != 0) {
                this.interaction.showError(`${resp.errorMsg}`);
            }
            this.handleNotify([resp], reqId);
         });
        this.renderProcess.on('pipe-stock-quote', (event, data) => { this.handlePipeMsg(data); });
    }

    requestTasks() {
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.query, {});
    }

    async requestAccounts() {

        if (this.isRequestingAccount) {
            return;
        }

        this.isRequestingAccount = true;

        try {

            let resp = await repoAccount.getAccountDetailInfo({ userId: this.userInfo.userId });
            let { errorCode, errorMsg, data } = resp;
            let records = (data || {}).list || [];
            let accounts = errorCode == 0 ? AccountSimple.Convert(records) : [];
            let balance = 0, available = 0, equity = 0, credit = 0;

            accounts.forEach(x => {

                balance += x.balance;
                available += x.available;
                equity += x.marketValue;
                credit += x.enableCreditBuy;
            });

            let ref = this.summary;
            ref.asset = balance;
            ref.available = available;
            ref.equity = equity;
            ref.credit = credit;
            this.setOfflineCounter(accounts);
            this.accounts = accounts;
            this.vtask && this.vtask.attachAccounts(accounts);
            this.vordered && this.vordered.attachAccounts(accounts);
        }
        catch(ex) {
            console.error(ex);
        }
        finally {
            this.isRequestingAccount = false;
        }
    }

    /**
     * @param {Array<AccountSimple>} accounts 
     */
    setOfflineCounter(accounts) {
        if (!this.offlineCounterFlag) {
            this.offlineCounterFlag = true;
            this.offlineCounter = accounts.every(x => x.connectionStatus === false);
            
            if (this.offlineCounter) {
              this.interaction.showWarning('交易柜台未连接', {
                duration: 0,
                showClose: false,
              })
            }
            // 从localStorage中恢复暂存的task和strategy
            this.vtask.recoverCacheTask();
        }
    }

    handleEnterStart(ev = { srcElement, target }) {

        console.log('enter pressed', { srcElement: ev.srcElement, target: ev.target });
        this.log(`enter key is pressed`);

        const ref = this.vtask;
        var ref2 = this.selected.task;
        var last = ref.lastTs;
        var now = new Date().getTime();

        /**
         * 如果合约待选列表，由回车选中，避免使用者敲击回车键出现粘滞效果，避免带出回车下单的非预期触发
         */
        var least = 600;
        var ellapsed = last >= 0 ? now - last : 0;
        var can_not_trigger_from_search = ref.isSearchFocused && ellapsed < least;

        ref.lastTs = 0;

        if (!ref2) {
            return;
        }
        else if (can_not_trigger_from_search) {
            return;
        }
        else if (this.isWindowBlockedByConfirm) {
            return;
        }

        let canStart = TaskObject.isTaskCreated(ref2.status) || TaskObject.isTaskPaused(ref2.status);
        if (canStart) {

            this.log(`enter key is accepted, the task is to be started: ${JSON.stringify(ref2)}`);
            this.handleStart();
        }
        else {
            this.log(`enter key is ignored`);
        }
    }


    /**
     * 响应快捷键捕捉同花顺和通达信窗口代码
     * @param {number} default_strategy_index 第几个默认策略（总共可支持2个）
     */
    handleStockCapture(default_strategy_index) {

        let start = Date.now();
        let stock = captureStockCode();
        let cost = Date.now() - start;
        console.log(`ths stock code`, { stock, cost, default_strategy_index });

        if (!stock) {

            this.interaction.showError('快捷键，未识别到同花顺合约');
            this.log(`captured no ths stock, cost = ${cost}ms`);
            return;
        }

        let capture_info = `captured ths stock = ${stock}, cost = ${cost}ms`;
        this.log(capture_info);
        console.log(capture_info);
        let matches = BizHelper.filterInstruments(this.systemEnum.assetsType.stock.code, stock);
        let stocks = matches.filter(x => x.exchangeId && (x.exchangeId.toUpperCase() == 'SHSE' || x.exchangeId.toUpperCase() == 'SZSE'));

        if (stocks.length == 0) {

            this.interaction.showError('未抓取到有效股票合约 = ' + stock);
            return;
        }

        if (this.ztsetting.ths.manualConfirm) {

            this.interaction.showConfirm({

                title: '操作确认',
                message: `添加合约${this.ztsetting.ths.immediate ? '并启动' : ''}：${stocks[0].instrument} / ${stocks[0].instrumentName} ？`,
                confirmed: () => {
                    this.createCaptureTask(stocks[0], default_strategy_index);
                },
            });
        }
        else {
            this.createCaptureTask(stocks[0], default_strategy_index);
        }
    }

    /**
     * @param {number} default_strategy_index 第几个默认策略（总共可支持2个）
     */
    createCaptureTask(stock, default_strategy_index) {

        const { strategy, strategy2, immediate } = this.ztsetting.ths;
        const which = default_strategy_index === 1 ? strategy : strategy2;
        this.vtask.handleThsMsg(stock.instrument, stock.instrumentName, which, immediate);
    }

    registerShortcuts() {

        var tags = ['BUTTON', 'INPUT'];
        // var blacks = ['smart-table-body', 'el-dialog__wrapper'];
        var blacks = ['el-dialog__wrapper'];

        document.addEventListener('click', () => {

            let $ele = event.target;
            if (tags.indexOf($ele.tagName) >= 0 || blacks.some(token => $ele.classList.contains(token))) {
                return;
            }

            let $cursor = $ele.parentElement;
            let isPermited = true;

            while ($cursor && $cursor.tagName != 'BODY') {

                let classes = $cursor.classList;
                let isPrevented = tags.some(token => $cursor.tagName == token);
                let isBlack = blacks.some(token => classes.contains(token));

                if (isPrevented || isBlack) {

                    isPermited = false;
                    break;
                }

                $cursor = $cursor.parentElement;
            }

            if (isPermited && this.vtask) {
                this.vtask.focusOnSearch();
            }
        });

        const ref = Mousetrap;
        ref.reset();

        /**
         * 窗口全局等待用户确认时，阻止所有快捷监听
         */
        ref.bind('enter', (e, combo) => { this.handleEnterStart(e); }, 'keyup');

        /**
         * 从授权文件获取是否启用了同花顺快捷键功能
         */

        if (this.isThsCaptureSupported()) {

            /**
             * 绑定同花顺快捷键
             */
            const key_stroke = this.app.shortcutOptions.ths_capture;
            const key_stroke_2 = this.app.shortcutOptions.ths_capture_2;

            if (key_stroke) {
                ref.bind(key_stroke, (e, combo) => { this.handleStockCapture(1); }, 'keyup');
            }

            if (key_stroke_2) {
                ref.bind(key_stroke_2, (e, combo) => { this.handleStockCapture(2); }, 'keyup');
            }
        }
    }

    isThsCaptureSupported() {

        // 是否启用了同花顺快捷键功能
        const is_enabled = this.app.shortcutOptions.enable_ths_shortcut === true;
        return is_enabled;
    }

    /**
     * @param {String} message
     */
    log(message) {
        this.loggerTrading.info('ztlog:' + message);
    }

    manageWindow() {

        this.winMgr = new WinPosSizeMgr('zt_20cm_trade', this.thisWindow);
        var data = this.winMgr.getLast();
        if (data) {
            this.winMgr.recover();
        }
        else {
            this.thisWindow.setSize(1766, 818);
        }

        this.winMgr.abserve();
    }

    /**
     * @param {HTMLElement} $container
     */
    async build($container) {

        super.build($container);

        this.buildLayout();
        await this.requestAccounts();
        // await this.requestOrderQueueCapab();

        this.createHeaderApp();
        this.createFooterApp();
        this.manageWindow();
        // 各个UI组件创建后，立即请求一次账号（用于挂载到策略列表、已委托列表）
        this.requestAccounts();

        await this.requestSettings();

        this.listen2Replies();
        this.listen2Notifies();
        setTimeout(() => { this.requestTasks(); }, 1000 * 1);
        setInterval(() => { this.overlapAll(true); }, 1000 * 30);
        this.registerShortcuts();
    }
};
