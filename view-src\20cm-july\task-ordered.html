<div class="trade-view-block task-ordered">

	<div class="view-toolbar s-pdl-10 s-pdr-15">
		<span class="view-title">已买监控</span>
		<el-button type="danger" size="mini" style="height: 20px; margin-top: 4px; margin-left: 10px;" @click="cancelChecks">撤勾选</el-button>
	</div>

	<div class="table-ordered-task s-full-height">
		<table>
			<tr>
				<th type="check" fixed-width="40"></th>
				<th label="证券代码" prop="instrument" watch="flagAsDiscarded" min-width="60" formatter="formatCodeCol" overflowt></th>
				<th label="证券名称" prop="instrumentName" watch="flagAsDiscarded" min-width="60" formatter="formatCellValue" overflowt></th>
				<th label="前序总量" prop="frontOrder" watch="flagAsDiscarded" min-width="70" class-maker="highlightCol" formatter="format2Int" thousands-int></th>
				<th label="前序总额" prop="frontOrderAmount" watch="flagAsDiscarded" min-width="80" class-maker="highlightCol" formatter="format2Int" thousands-int></th>
				<th label="封单量" prop="limitBuy" watch="flagAsDiscarded" min-width="70" class-maker="highlightCol" formatter="format2Int" thousands-int></th>
				<th label="封单金额" prop="limitBuyAmount" watch="flagAsDiscarded" min-width="80" class-maker="highlightCol" formatter="format2Float" thousands-int></th>
				<th label="委托数量" prop="targetVolume" watch="flagAsDiscarded" min-width="70" align="right" formatter="format2Int" thousands-int overflowt></th>
				<th label="成交数量" prop="tradedVolume" watch="flagAsDiscarded" min-width="70" align="right" formatter="format2Int" thousands-int overflowt></th>
				<th label="委托金额" prop="usedMargin" watch="flagAsDiscarded" min-width="80" align="right" formatter="format2Float" thousands-int overflowt></th>
				<th label="委托时间" prop="orderTime" watch="flagAsDiscarded" min-width="70" formatter="formatTimeCol"></th>
				<th label="操作" watch="flagAsDiscarded" fixed-width="60" formatter="formatOper" exportable="false"></th>
			</tr>
		</table>
	</div>
</div>
