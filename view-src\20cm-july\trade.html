<div class="v20cm-july">

	<!-- 按钮绝对定位，固定右上角 -->

	<span class="setting-btn">

		<el-button size="mini" type="primary" @click="openSetting">
			<i class="el-icon-s-tools"></i>
			<span>交易设置</span>
		</el-button>

		<el-button size="mini" type="primary" @click="openRingtonSetting">
			<i class="el-icon-setting"></i>
			<span>交易铃音设置</span>
		</el-button>

		<el-button size="mini" type="primary" @click="syncAccountData">
			<i class="el-icon-coin"></i>
			<span>数据同步</span>
		</el-button>

	</span>

	<div class="part-left-view">
		<div class="view-task left-part regular-view"></div>
		<div class="view-strategy regular-view"></div>
		<div class="view-queue regular-view"></div>
	</div>

	<div class="part-right-view">
		<div class="view-task right-part regular-view"></div>
		<div class="view-main"></div>
	</div>

	<div class="footer-row">
		<div class="footer-row-inner">
			<template>
				<span class="summary-item">总资产 {{ thousandsInt(summary.asset) }}</span>
				<span class="summary-item">可用 {{ thousandsInt(summary.available) }}</span>
				<span class="summary-item">可融 {{ thousandsInt(summary.credit) }}</span>
				<span class="summary-item">市值 {{ thousandsInt(summary.equity) }}</span>
			</template>
		</div>
	</div>

	<div class="setting-view-box"></div>
	<div class="rington-view-box"></div>
	
</div>
