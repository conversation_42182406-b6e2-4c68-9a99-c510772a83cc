const BaseComponent = require('../../../admin/indicator/components/BaseComponent');
const { algorithmTypeEnum, fullParams, fullParamsEnum } = require('../../../../config/auto-sell');

module.exports = class DragWidget extends BaseComponent {
  constructor() {
    super(__dirname);
    const self = this;
    this.options = {
      props: {
        visible: {
          required: true,
          type: Boolean,
        },
        item: {
          type: Object,
        },
      },
      data() {
        return {
          helper: self.parent.helper,
          form: {
            algorithmType: '',
            confirmTwice: true,
          },
          rules: {
            algorithmType: [{ required: true, message: '请选择算法类型', trigger: 'change' }],
          },
          algorithmTypes: [algorithmTypeEnum.定时定量, algorithmTypeEnum.低封单量, algorithmTypeEnum.定时跟量, algorithmTypeEnum.定涨定量, algorithmTypeEnum.定时卖],
          paramForm: {},
          fullParams: fullParams,
          tempSetting: {},
        };
      },
      computed: {
        paramRules() {
          let rules = {};

          this.params.forEach((param) => {
            if (param.rules) {
              rules[param.prop] = param.rules;
            }
          });

          return rules;
        },
        title() {
          return '修改默认参数';
        },
        algorithmName() {
          let matched = this.algorithmTypes.find((x) => x.value == this.form.algorithmType);
          return matched ? matched.label : '';
        },
        params() {
          return this.fullParams[this.form.algorithmType];
        },
        displayParams() {
          return this.params.filter((x) => {
            if (x.show === false && x.isCancel) {
              return false;
            }
            if (x.type === 'display') {
              return false;
            }
            if (x.noDefault) {
              return false;
            }
            return true;
          });
        },
      },
      watch: {
        visible(val) {
          if (val) {
            this.setData();
          } else {
            this.resetData();
          }
        },
      },

      methods: {
        initTemplateSetting(reset = false) {
          this.algorithmTypes.forEach((item) => {
            let form = {};
            this.fullParams[item.value].forEach((param) => {
              let defaultValue;
              if (reset) {
                if (param.default !== undefined && param.default !== null) {
                  defaultValue = param.default;
                } else {
                  defaultValue = undefined;
                }
              } else {
                let defaultParams = this.item[item.value];
                if (defaultParams && defaultParams[param.prop] !== undefined && defaultParams[param.prop] !== null) {
                  defaultValue = defaultParams[param.prop];
                } else {
                  if (param.default !== undefined && param.default !== null) {
                    defaultValue = param.default;
                  } else {
                    defaultValue = undefined;
                  }
                }
              }
              form[param.prop] = defaultValue;
            });
            Vue.set(this.tempSetting, item.value, form);
          });
        },
        getDefaultParamValue(param) {
          let defaultParams = this.item[this.form.algorithmType];
          if (defaultParams && defaultParams[param.prop] !== undefined && defaultParams[param.prop] !== null) {
            return defaultParams[param.prop];
          } else {
            if (param.default !== undefined && param.default !== null) {
              return param.default;
            } else {
              return undefined;
            }
          }
        },
        setData() {
          this.initTemplateSetting();
          console.log('set data: ', this.item);
          this.form.confirmTwice = this.item.confirmTwice !== undefined ? this.item.confirmTwice : true;
          this.form.algorithmType = algorithmTypeEnum.定时定量.value;
          this.handleChangeAlgorithmType();
          // this.params.forEach((param) => {
          //   let defaultValue = this.getDefaultParamValue(param);
          //   this.paramForm[param.prop] = defaultValue;
          // });
        },
        resetData() {
          this.$nextTick(async () => {
            this.form = {
              algorithmType: '',
              confirmTwice: true,
            };
            this.paramForm = {};
            await self.parent.helper.sleep(200);
            if (this.$refs.paramForm) {
              this.$refs.paramForm.clearValidate();
            }
            this.$refs.form.clearValidate();
          });
        },
        handleChangeAlgorithmType() {
          this.initParamForm();
          this.$nextTick(() => {
            this.$refs.paramForm.clearValidate();
          });
        },
        initParamForm() {
          let tempForm = this.tempSetting[this.form.algorithmType];
          if (tempForm) {
            Vue.set(this, 'paramForm', tempForm);
          } else {
            let form = {};
            this.params.forEach((item) => {
              let defaultValue = this.getDefaultParamValue(item);
              form[item.prop] = defaultValue;
            });
            Vue.set(this, 'paramForm', form);
          }
        },
        cancel() {
          this.$emit('update:visible', false);
        },
        reset() {
          this.params.forEach((item) => {
            if (item.default !== undefined) {
              this.paramForm[item.prop] = item.default;
            } else {
              this.paramForm[item.prop] = undefined;
            }
          });
          // delete this.tempSetting[this.form.algorithmType];
          this.initTemplateSetting(true);
          this.form.confirmTwice = true;
        },
        save() {
          this.$refs.form.validate((valid) => {
            if (valid) {
              this.$refs.paramForm.validate((valid) => {
                if (valid) {
                  console.log('save', this.tempSetting);
                  this.$emit('save', {
                    ...this.tempSetting,
                    confirmTwice: this.form.confirmTwice,
                  });
                  this.cancel();
                }
              });
            }
          });
        },
        updateSetting() {
          console.log('updateSetting');
          Vue.set(this.tempSetting, this.form.algorithmType, this.paramForm);
        },
      },
    };
  }
};
