const { BaseView } = require('./base-view');
const { B<PERSON><PERSON>el<PERSON> } = require('../../libs/helper-biz');
const { repo20Cm } = require('../../repository/20cm');

class AbnormalStockPoolView extends BaseView {
    constructor(view_name) {
        super(view_name);
    }

    createApp() {
        this.vueApp = new Vue({
            el: this.$container.querySelector('.abnormal-stock-pool-view'),
            data: {
                interval: null,
                state: {
                    searchText: '',
                },
                riskData: [],
                columns: [
                    {
                        prop: 'id',
                        label: '交易所',
                        render: (row) => {
                            const exch = row.instrument.split('.')[0];
                            if (exch.includes('SH')) {
                                return '上交所';
                            } else if (exch.includes('SZ')) {
                                return '深交所';
                            } else if (exch.includes('BJ')) {
                                return '北交所';
                            } else {
                                return exch;
                            }
                        },
                        width: 100,
                    },
                    { prop: 'instrument', label: '证券代码', width: 100 },
                    { prop: 'instrumentName', label: '证券名称', width: 100 },
                    { prop: 'beginDate', label: '开始日期', width: 100 },
                    { prop: 'endDate', label: '结束日期', width: 100 },
                    { prop: 'reason', label: '原因' },
                    { prop: 'riskPrompt', label: '风险提示' },
                    {
                        prop: 'riskType',
                        label: '类型',
                        render: (row) => {
                            const type = row.riskType;
                            if (type == 9) {
                                return '重点关注';
                            } else if (type == 10) {
                                return '异常波动';
                            } else {
                                return type;
                            }
                        },
                        width: 100,
                    },
                ],
            },
            computed: {
                filteredData() {
                    return this.riskData.filter((item) => {
                        return item.instrument.includes(this.state.searchText) || item.instrumentName.includes(this.state.searchText);
                    });
                },
            },
            mounted() {
                this.requestData();
                this.interval = setInterval(() => {
                    this.requestData();
                }, 1000 * 60 * 30);
            },
            beforeDestroy() {
                clearInterval(this.interval);
            },
            methods: {
                refresh() {
                    this.requestData();
                },
                async requestData() {
                    var resp = await repo20Cm.queryRisk();
                    if (resp.errorCode != 0) {
                        this.riskData = [];
                    }

                    this.riskData = resp.data;
                },
            },
        });
    }

    dispose() {
        this.vueApp.$destroy();
    }

    createTable() {
        return;
    }

    build($container) {
        super.build($container);
        this.createApp();
    }
}

module.exports = AbnormalStockPoolView;
