<el-dialog custom-class="algorithm-dialog" width="430px" :title="title" :visible.sync="visible" :show-close="false" :close-on-click-modal="false" @close="cancel">
  <div class="s-pd-10" style="padding-left: 0">
    <el-form size="mini" class="form" label-width="120px" ref="form" :model="form" :rules="rules">
      <el-form-item label="股票代码" prop="instrumentName">
        <el-autocomplete
          placeholder="输入代码或名称"
          class=""
          v-model="form.instrumentName"
          :fetch-suggestions="handleSuggest"
          trigger-on-focus
          @keydown.native="handleInput"
          @clear="handleClear"
          @select="handleSelect"
          prefix-icon="iconfont icon-sousuo"
          clearable
        >
          <template slot-scope="{ item: ins }">
            <span class="item-name">{{ ins.instrumentName }} </span>
            <span class="item-code">[{{ ins.instrument }}]</span>
          </template>
        </el-autocomplete>
      </el-form-item>
      <el-form-item label="算法名称" prop="algorithmType">
        <el-select :disabled="disableType" v-model="form.algorithmType" placeholder="请选择算法" @change="handleChangeAlgorithmType">
          <el-option v-for="item in algorithmTypes" :key="item.value" :label="item.label" :value="item.value">
            <el-tooltip placement="top" :content="item.desp">
              <i class="option-icon el-icon-question"></i>
            </el-tooltip>
            <span style="margin-left: 5px" class="option-label">{{ item.label }}</span>
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="可卖数量" prop="canSellVolume">
        <div class="number-wrapper">
          <el-input-number :controls="false" disabled v-model="form.canSellVolume" placeholder=""> </el-input-number>
          <div class="unit s-fs-16">手</div>
        </div>
      </el-form-item>
    </el-form>
    <div class="param-setting">
      <div class="hint-row">
        <div class="title s-fs-16">参数设置</div>
        <div v-if="!form.algorithmType" class="hint">请先选择算法</div>
      </div>
      <div v-if="!!form.algorithmType">
        <div class="param-list">
          <el-form :key="form.algorithmType" size="mini" class="form" label-width="150px" ref="paramForm" :model="paramForm" :rules="paramRules">
            <el-form-item v-for="(item, index) in displayParams" :key="index" :label="item.label" :prop="item.prop" :class="item.className">
              <div class="form-label" slot="label">
                <i v-if="item.alter" @click="handleSwitchProp(item)" class="s-cp label-icon el-icon-refresh"></i>
                <div class="label">{{ item.label }}</div>
                <el-tooltip placement="top" :content="item.desp">
                  <i class="label-icon el-icon-question"></i>
                </el-tooltip>
              </div>
              <el-select v-if="item.type === 'select'" v-model="paramForm[item.prop]" :placeholder="item.placeholder">
                <el-option v-for="(option, optionIndex) in item.options" :key="optionIndex" :label="option.label" :value="option.value"></el-option>
              </el-select>
              <div v-else-if="item.type === 'number'" class="number-wrapper">
                <el-input-number
                  :controls="false"
                  v-model="paramForm[item.prop]"
                  :placeholder="item.placeholder"
                  :min="item.min"
                  :max="item.max"
                  :disabled="item.prop === 'cancelProtectedTime' && shouldDisableCancelParam"
                  @change="handleValueChange(item)"
                >
                </el-input-number>
                <div v-if="item.unit" class="unit s-fs-16">{{ item.unit }}</div>
              </div>
              <div v-else-if="item.type === 'display'" class="display-wrapper">
                <span class="display-value">{{ getDisplayValue(item) }}</span>
              </div>
              <div v-else-if="item.type === 'time'" class="time-with-ms-wrapper">
                <el-time-picker v-model="paramForm[item.prop]" :placeholder="item.placeholder" format="HH:mm:ss" value-format="HHmmss" @change="handleValueChange(item)" style="width: 140px">
                </el-time-picker>
                <el-input-number
                  class="time-with-ms-input"
                  v-model="paramForm[item.prop + 'Ms']"
                  :min="0"
                  :max="999"
                  :step="1"
                  :controls="false"
                  placeholder="毫秒"
                  @change="handleValueChange(item)"
                ></el-input-number>
                <span style="margin-left: 5px">毫秒</span>
              </div>
              <!-- <el-input v-else v-model="paramForm[item.prop]" :placeholder="item.placeholder">
                <template v-if="item.unit" slot="append">{{ item.unit }}</template>
              </el-input> -->
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>

  <div slot="footer">
    <el-button @click="cancel" type="info" size="mini" class="s-fs-16">取消</el-button>
    <el-button @click="save" type="primary" size="mini" class="s-fs-16">保存</el-button>
    <el-button v-if="!disableType" @click="saveStart" type="primary" size="mini" class="s-fs-16">保存并启动</el-button>
  </div>
</el-dialog>
