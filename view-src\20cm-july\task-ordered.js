const { BaseView } = require('./base-view');
const { SmartTable } = require('../../libs/table/smart-table');
const { Cm20FunctionCodes } = require('../../config/20cm');
const { BuyTask, ZtStandardStrategy, FromTable, RowBehaviors, ZtSetting } = require('./objects');
const { BoughtOrderFrontOrder } = require('../../model/front-order');
const { repoOrder } = require('../../repository/order');
const { BizHelper } = require('../../libs/helper-biz');

module.exports = class OrderedTaskView extends BaseView {

    constructor() {

        super('@20cm-july/task-ordered', false, '已买监控');
        this.ztsetting = ZtSetting.makeDefault();
    }

    /**
     * @returns {BuyTask}
     */
    typeds(data) {
        return data;
    }

    /**
     * @param {BuyTask} record
     */
    identify(record) {
        return record.id;
    }

    createTable() {

        const $table = this.$container.querySelector('.table-ordered-task');
        const ref = new SmartTable($table, this.identify, this, {

            tableName: 'smt-20cm-task-ordered',
            displayName: this.title,
            defaultSorting: { prop: 'createTime', direction: 'desc' },
            rowSelected: this.handleRowSelect.bind(this),
            rowDbClicked: this.handleRowDbClick.bind(this),
        });

        ref.setPageSize(99999);
        return ref;
    }

    /**
     * @param {BuyTask} task
     */
    handleRowSelect(task) {

        if (task.flagAsDiscarded === true) {
            return;
        }

        this.log(`user selected an ordered task: ${JSON.stringify(task)}`);
        this.tolerate(FromTable.ordered, RowBehaviors.select, task);
    }

    /**
     * @param {BuyTask} task
     */
    handleRowDbClick(task) {

        if (!this.ztsetting.doubleClick2Cancel) {
            return;
        }

        if (task.flagAsDiscarded === true) {
            return;
        }

        this.log(`user double clicked an ordered task: ${JSON.stringify(task)}`);
        this.cancelTask(task);
    }

    tolerate(from, behavior, task) {
        this.trigger('tolerate', from, behavior, task);
    }

    sortableRows() {

        var ref = $(this.tableObj.$bodyTable.querySelector('tbody'));
        ref.sortable();
    }

    refresh() {

        this.interaction.showSuccess('刷新请求已发出');
        this.trigger('refresh-task');
    }

    download() {
        this.tableObj.exportAllRecords();
    }

    highlightCol() {
        return 's-color-red';
    }

    /**
     * @param {BuyTask} task
     */
    cancelTask(task) {

        this.log(`to cancel an ordered task: ${JSON.stringify(task)}`);
        this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: task.id });
        this.interaction.showSuccess('撤单请求已提交');
    }

    /**
     * @param {BuyTask} task
     */
    removeTask(task) {

        this.log(`to remove a discarded task: ${JSON.stringify(task)}`);
        const row_key = this.identify(task);

        if (this.tableObj.hasRow(row_key)) {
            this.tableObj.deleteRow(row_key);
        }
    }

    /**
     * @param {BuyTask} task
     */
    outcancel(task) {

        if (!this.tableObj.hasRow(this.identify(task))) {
            return this.interaction.showError(`未匹配到可撤监控：${task.instrumentName}，请尝试从列表操作！`);
        }

        this.cancelTask(task);
    }

    /**
     * @param {BuyTask} task
     * @param {string} instrument
     */
    formatCodeCol(task, instrument) {
        return task.flagAsDiscarded === true ? '--' : this.shortizeCode(instrument);
    }

    /**
     * @param {BuyTask} task
     * @param {number} timestamp
     */
    formatTimeCol(task, timestamp) {
        return task.flagAsDiscarded === true ? '--' : new Date(timestamp).format('hh:mm:ss');
    }

    /**
     * @param {BuyTask} task
     */
    formatCellValue(task, value, name) {
        return task.flagAsDiscarded === true || this.helper.isNone(value) ? '--' : value;
    }

    /**
     * @param {BuyTask} task
     * @param {number} value
     */
    format2Int(task, value, name) {

        if (task.flagAsDiscarded === true || this.helper.isNone(value) || typeof value != 'number') {
            return '--';
        }
        else {
            return value.thousands();
        }
    }

    /**
     * @param {BuyTask} task
     * @param {number} value
     */
    format2Float(task, value, name) {

        if (task.flagAsDiscarded === true || this.helper.isNone(value) || typeof value != 'number') {
            return '--';
        }
        else if (value >= 10000) {
            return (value / 10000).thousandsDecimal() + '万';
        }
        else {
            return value.thousandsDecimal();
        }
    }

    /**
     * @param {BuyTask} task
     */
    formatOper(task) {
        return task.flagAsDiscarded === true ? '<button class="danger" event.onclick="removeTask">删除</a>' : '<button class="danger" event.onclick="cancelTask">撤单</a>';
    }

    /**
     * @param {Array<BuyTask>} tasks
     */
    push(tasks) {

        tasks.forEach(item => {

            const row_key = this.identify(item);
            const should_keep = (this.isTaskOrdered(item.status) || this.isSupplemented(item.status)) && !item.hasCanceled;
            const tobj = this.tableObj;
            const has_matched = tobj.hasRow(row_key);
            const is_new = !has_matched;

            if (should_keep) {

                if (is_new) {

                    // 新增，默认插入到上面表格
                    tobj.insertRow(item);
                    this.ring4TaskBeenMonitored(item);
                }
                else {

                    const matched = this.typeds(has_matched ? tobj.getRowData(row_key) : null);
                    const is_discarded_before = matched && matched.flagAsDiscarded === true;

                    if (has_matched) {
                        tobj.updateRow(item);
                    }

                    if (is_discarded_before) {
                        this.ring4TaskBeenMonitored(item);
                    }
                }
            }
            else {

                if (has_matched) {

                    item.flagAsDiscarded = true;
                    tobj.updateRow(item);
                }
            }
        });

        if (this.hasInitializedFrontOrder == undefined) {

            this.hasInitializedFrontOrder = true;
            this.updateFrontOrder();
        }
    }

    /**
     * @param {BuyTask} task 来源监控任务
     * @param {ZtStandardStrategy} strategy
     */
    outupdate(task, strategy) {

        let row_key = this.identify(task);
        let expected = this.typeds(this.tableObj.getRowData(row_key));
        if (!expected) {
            return;
        }

        this.tableObj.updateRow({

            id: expected.id,
            localId: expected.localId,
            strategyName: strategy.name,
            strategy: strategy,
        });

        this.log(`update an ordered task strategy from outside: task id/${task.id}, strategy/${JSON.stringify(strategy)}`);
        this.toSubmit(expected, Cm20FunctionCodes.request.modify, true);
    }

    createToolbarApp() {

        const vappIns = new Vue({

            el: this.$container.querySelector('.view-toolbar'),
            data: {
                //
            },
            methods: this.helper.fakeVueInsMethod(this, [
                this.cancelChecks,
            ]),
        });
    }

    cancelChecks() {

        var checkes = this.tableObj.extractCheckedRecords().map(x => this.typeds(x));
        var undiscardeds = checkes.filter(x => x.flagAsDiscarded !== true);

        if (checkes.length == 0) {
            return this.interaction.showError('未有勾选的监控');
        }
        else if (undiscardeds.length == 0) {
            return this.interaction.showError('勾选的监控已作废');
        }

        this.log(`to cancel checked tasks: ${JSON.stringify(undiscardeds.map(x => ({ instrument: x.instrument, id: x.id })))}`);
        undiscardeds.forEach(x => {
            this.renderProcess.send(this.systemEvent.transmitMsg2TradingServer, Cm20FunctionCodes.request.cancel, { id: x.id });
        });
        this.interaction.showSuccess('撤单请求已提交，数量 = ' + undiscardeds.length);
    }

    /**
     * @param {ZtSetting} setting
     */
    setAsSetting(setting) {
        Object.assign(this.ztsetting, this.helper.deepClone(setting));
    }

    async updateFrontOrder() {

        if (this.isProcessing) {
            return;
        }

        if (this.tableObj.rowCount == 0) {
            return;
        }

        try {

            this.isProcessing = true;
            let resp = await repoOrder.queryFrontOrder();
            let { errorCode, errorMsg, data } = resp;

            if (errorCode == 0 && this.helper.isJson(data)) {

                const fronts = BoughtOrderFrontOrder.convert(data);
                const tobj = this.tableObj;

                fronts.forEach(item => {

                    /**
                     * 对匹配的数据行进行更新
                     */
                    if (tobj.hasRow(item.orderId)) {

                        let row = tobj.getRowData(item.orderId);
                        tobj.updateRow({

                            id: item.orderId,
                            frontOrder: item.frontOrder / 100,
                            frontOrderAmount: (item.frontOrder * BizHelper.pick(row.instrument).upperLimitPrice / 10000),
                            limitBuy: item.limitBuy / 100,
                            limitBuyAmount: (item.limitBuy * BizHelper.pick(row.instrument).upperLimitPrice / 10000),
                        });
                    }
                    else {
                        //
                    }
                });
            }
            else {
                console.error(resp);
            }
        }
        catch (ex) {
            //
        }
        finally {
            this.isProcessing = false;
        }
    }

    build($container) {

        super.build($container);
        this.sortableRows();
        this.createToolbarApp();
        setInterval(() => { this.updateFrontOrder(); }, 1000 * 3);
    }
};
