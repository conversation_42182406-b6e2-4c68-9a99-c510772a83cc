
const MainModule = require('../main-module').MainModule;
const ServerEventEmitter = require('../component/server-event-emitter').ServerEventEmitter;
const Server = require('./server').Server;

class ServerManager extends MainModule {

    get isTradingServerConnected() {
        return this.tradingServer && this.tradingServer.isConnected;
    }

    get isQuoteServerConnected() {
        return this.quoteServer && this.quoteServer.isConnected;
    }

    constructor() {
        super('server-manager');
    }

    /**
     * create a brandly new [trading] server
     */
    createNewTradingServer(server_info, core_handlers) {

        var event_emitter = new ServerEventEmitter(core_handlers);
        this.tradingServer = new Server('trading', server_info, event_emitter, (is_connected) => {
            this.app.serverStates.isTradingServerConnected = is_connected; 
        });

        var log_msg = `server manager > created a new [trading server] ${JSON.stringify(server_info)}`;
        this.loggerSys.info(log_msg);
        this.loggerConsole.info(log_msg);
        this.loggerSys.info(log_msg);
    }

    /**
     * create a brandly new [quote] server
     */
    createNewQuoteServer(server_info, core_handlers) {

        var event_emitter = new ServerEventEmitter(core_handlers);
        this.quoteServer = new Server('quote', server_info, event_emitter, (is_connected) => {
            this.app.serverStates.isQuoteServerConnected = is_connected; 
        });

        var log_msg = `server manager > created a new [quote server] ${JSON.stringify(server_info)}`;
        this.loggerSys.info(log_msg);
        this.loggerConsole.info(log_msg);
        this.loggerSys.info(log_msg);
    }

    /**
     * connect to trading server
     */
    connect2TradingServer(another_server) {
        this.tradingServer.connect(another_server);
    }

    /**
     * connect to quote server
     */
    connect2QuoteServer(another_server) {
        this.quoteServer.connect(another_server);
    }

    /**
     * login onto trading server
     */
    logonTradingServer() {

        if (!this.isTradingServerConnected) {

            let msg = `server manager > [trading server] ${this.tradingServer.displayName} is not initialized or disconnected, cannot login`;
            this.loggerConsole.info(msg);
            this.loggerSys.info(msg);
            return;
        }

        let login_input = this.getLoginUserInput();
        let message_body = {

            loginType: login_input.loginType || 0,
            branchId: login_input.branchId,
            orgId: login_input.orgId,
            credit: login_input.credit,
            userName: login_input.userName,
            password: login_input.passCode,
            mac: login_input.macAddr,
            os: login_input.os,
            md5: login_input.md5,
            sessionId: '0',
            configStr: login_input.configStr,
        };

        if (this.app.offlineOptions && this.app.offlineOptions.offlineLogin) {
          message_body.counterOffline = 1;
        }

        if (this.getContextDataItem('is-by-reconnect')) {

            this.setContextDataItem('is-by-reconnect', false);
            message_body.reLogin = 1;
        }
        
        let cloned = Object.assign({}, message_body);
        delete cloned.password;
        console.log(cloned);

        let message = { fc: this.serverFunction.userLogin, reqId: 0, dataType: 6, body: message_body };
        this.loggerSys.info(`server manager > send command to login [trading server] > begin > ${this.tradingServer.displayName}`);
        this.tradingServer.send(message);
        this.loggerSys.info(`server manager > send command to login [trading server] > end > ${this.tradingServer.displayName}`);
    }

    
    /**
     * login onto quote server
     */
    logonQuoteServer() {

        if (!this.isQuoteServerConnected) {

            let msg = `server manager > [quote server] ${this.quoteServer.displayName} is not initialized or disconnected, cannot login`;
            this.loggerConsole.info(msg);
            this.loggerSys.info(msg);
            return;
        }

        let login_input = this.getLoginUserInput();
        let message_body = {

            loginType: login_input.loginType || 0,
            branchId: login_input.branchId,
            orgId: login_input.orgId,
            credit: login_input.credit,
            userName: login_input.userName,
            password: login_input.passCode,
            mac: login_input.macAddr,
            os: login_input.os,
            md5: login_input.md5,
            sessionId: '0',
            configStr: login_input.configStr,
        };
        
        let message = { fc: this.serverFunction.userLoginQuote, reqId: 0, dataType: 5, body: message_body };
        this.loggerSys.info(`server manager > send command to login [quote server] > begin > ${this.quoteServer.displayName}`);
        this.quoteServer.send(message);
        this.loggerSys.info(`server manager > send command to login [quote server] > end > ${this.quoteServer.displayName}`);
    }
}


module.exports = { ServerManager };