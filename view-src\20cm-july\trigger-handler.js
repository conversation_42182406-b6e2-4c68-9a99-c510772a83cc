const { CriteriaParam, BuyCancelTrigger } = require('./objects');
const { helper } = require('../../libs/helper');
const { repoInstrument } = require('../../repository/instrument');

/**
 * 买入策略的价格类型变化处理函数
 * @param {CriteriaParam} cp 当前输入项
 * @param {BuyCancelTrigger} trigger 触发项
 * @param {string} instrument 当前合约
 */
async function customPriceTypeChange(cp, trigger, instrument) {
    
    if (cp.variable != 'customPriceType') {
        return;
    }

    let matched = trigger.conditions.find(x => x.variable == 'customPrice');
    if (!matched) {
        return;
    }

    if (!instrument) {

        matched.threshold = null;
        return;
    }

    let resp = await repoInstrument.queryPrice(instrument);
    let { errorCode, errorMsg, data } = resp;
    if (!helper.isJson(data)) {

        // 未获取到价格，将自定义价格项置空
        matched.threshold = null;
        return;
    }

    const upper = +data.upperLimitPrice.toFixed(2);
    const lower = +data.lowerLimitPrice.toFixed(2);
    const types = [1, 2, 3];

    if (types.find(x => x == cp.threshold) == undefined) {
        cp.threshold = types[0];
    }

    if (cp.threshold == 1) {
        
        // 涨停价格
        matched.threshold = upper;
    }
    else if (cp.threshold == 2) {
        
        // 笼子价格
        let cage_price = +(Math.floor(upper * 0.98 * 100) / 100).toFixed(2);
        matched.threshold = Math.min(cage_price, upper);
    }
    else if (cp.threshold == 3) {
        
        // 自定义价格
        let custom_price = matched.threshold;

        if (typeof custom_price == 'number') {

            // 选择的是自定义价格，且价格字段有值
            if (custom_price > upper || custom_price < lower) {

                // 已具备的自定义价格，超出了涨跌停价格
                matched.threshold = upper;
            }
            else {
                // 具备的自定义价格，在涨跌停价格内，不做处理
            }
        }
        else {

            // 选择自定义价格时，如果尚未设置过价格字段，则默认为涨停价
            matched.threshold = upper;
        }
    }
    else {
        // 授权文件配置参数项错误
    }
}

const TriggerHandlers = {
    customPriceTypeChange,
};

module.exports = { TriggerHandlers };
